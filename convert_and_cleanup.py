#!/usr/bin/env python3
"""
自动转换YOLO模型为ONNX格式并清理文件的脚本
功能：
1. 将所有文件夹下weights中的best.pt转为onnx
2. 重命名onnx文件为对应文件夹名称
3. 删除除了pt和onnx外的所有文件和文件夹
"""

import os
import subprocess
import shutil
import glob
from pathlib import Path

def run_command(command, cwd=None):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, 
                              capture_output=True, text=True, check=True)
        print(f"✓ 成功执行: {command}")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        print(f"✗ 执行失败: {command}")
        print(f"错误信息: {e.stderr}")
        return False, e.stderr

def find_model_folders():
    """查找包含weights文件夹的模型目录"""
    current_dir = Path('.')
    model_folders = []
    
    for item in current_dir.iterdir():
        if item.is_dir() and item.name.startswith('detect_cat_dog_'):
            weights_dir = item / 'weights'
            if weights_dir.exists() and (weights_dir / 'best.pt').exists():
                model_folders.append(item.name)
    
    return model_folders

def convert_to_onnx(folder_name):
    """将指定文件夹下的best.pt转换为onnx"""
    model_path = f"{folder_name}/weights/best.pt"
    print(f"\n正在转换 {model_path} 为 ONNX 格式...")
    
    command = f"yolo export model={model_path} format=onnx"
    success, output = run_command(command)
    
    if success:
        print(f"✓ {folder_name} 转换成功")
        return True
    else:
        print(f"✗ {folder_name} 转换失败")
        return False

def rename_onnx_file(folder_name):
    """重命名onnx文件为文件夹名称"""
    weights_dir = Path(folder_name) / 'weights'
    best_onnx = weights_dir / 'best.onnx'
    target_onnx = weights_dir / f'{folder_name}.onnx'
    
    if best_onnx.exists():
        try:
            best_onnx.rename(target_onnx)
            print(f"✓ 重命名 {best_onnx} -> {target_onnx}")
            return True
        except Exception as e:
            print(f"✗ 重命名失败: {e}")
            return False
    else:
        print(f"✗ 找不到 {best_onnx}")
        return False

def cleanup_files(folder_name):
    """删除除了pt和onnx外的所有文件和文件夹"""
    folder_path = Path(folder_name)
    weights_dir = folder_path / 'weights'
    
    print(f"\n正在清理 {folder_name} 文件夹...")
    
    # 保留weights文件夹中的.pt和.onnx文件
    if weights_dir.exists():
        for item in weights_dir.iterdir():
            if item.is_file() and item.suffix not in ['.pt', '.onnx']:
                try:
                    item.unlink()
                    print(f"✓ 删除文件: {item}")
                except Exception as e:
                    print(f"✗ 删除文件失败 {item}: {e}")
    
    # 删除主文件夹中除weights外的所有内容
    for item in folder_path.iterdir():
        if item.name != 'weights':
            try:
                if item.is_file():
                    item.unlink()
                    print(f"✓ 删除文件: {item}")
                elif item.is_dir():
                    shutil.rmtree(item)
                    print(f"✓ 删除文件夹: {item}")
            except Exception as e:
                print(f"✗ 删除失败 {item}: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("YOLO模型转换和清理脚本")
    print("=" * 60)
    
    # 查找模型文件夹
    model_folders = find_model_folders()
    
    if not model_folders:
        print("未找到包含best.pt的模型文件夹")
        return
    
    print(f"找到 {len(model_folders)} 个模型文件夹:")
    for folder in model_folders:
        print(f"  - {folder}")
    
    # 确认是否继续
    response = input("\n是否继续执行转换和清理操作？(y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("操作已取消")
        return
    
    # 执行转换和清理
    success_count = 0
    
    for folder_name in model_folders:
        print(f"\n{'='*40}")
        print(f"处理文件夹: {folder_name}")
        print(f"{'='*40}")
        
        # 步骤1: 转换为ONNX
        if convert_to_onnx(folder_name):
            # 步骤2: 重命名ONNX文件
            if rename_onnx_file(folder_name):
                # 步骤3: 清理文件
                cleanup_files(folder_name)
                success_count += 1
                print(f"✓ {folder_name} 处理完成")
            else:
                print(f"✗ {folder_name} 重命名失败，跳过清理")
        else:
            print(f"✗ {folder_name} 转换失败，跳过后续步骤")
    
    print(f"\n{'='*60}")
    print(f"处理完成！成功处理 {success_count}/{len(model_folders)} 个文件夹")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
